# 多平台Cookie提取工具

一个基于Python的图形化Cookie提取工具，支持头条、百家、抖音、B站、闲鱼、快手等主流平台。

## 功能特点

- 🌐 支持6大主流平台：头条、百家、抖音、B站、闲鱼、快手
- 🖥️ 现代化GUI界面，基于CustomTkinter
- 🍪 自动提取完整Cookie信息
- 💾 标准txt格式保存，兼容性好
- 🔄 自动尝试退出当前账号
- 💾 配置持久化，记住用户设置

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行程序：
```bash
python cookie_extractor.py
```

2. 选择要提取Cookie的平台

3. 设置Cookie保存路径

4. 点击"启动浏览器"按钮

5. 在打开的浏览器中完成登录（扫码或密码登录）

6. 点击"获取当前CK"按钮提取Cookie

7. Cookie将自动保存为txt格式文件

## 文件说明

- `cookie_extractor.py` - 主程序文件
- `requirements.txt` - 依赖包列表
- `config.json` - 配置文件（自动生成）

## 注意事项

- 首次运行需要安装Chrome浏览器
- 确保网络连接正常
- 某些平台可能有反自动化检测，请正常使用
- Cookie文件包含敏感信息，请妥善保管

## 支持平台

| 平台 | URL |
|------|-----|
| 头条 | https://mp.toutiao.com/ |
| 百家 | https://baijiahao.baidu.com/ |
| 抖音 | https://creator.douyin.com/ |
| B站 | https://member.bilibili.com/ |
| 闲鱼 | https://www.goofish.com/ |
| 快手 | https://cp.kuaishou.com/ |
