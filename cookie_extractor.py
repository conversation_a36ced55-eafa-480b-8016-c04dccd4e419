#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多平台Cookie提取工具
支持头条、百家、抖音、B站、闲鱼、快手等平台的Cookie提取
"""

import os
import json
import time
import threading
from datetime import datetime
from tkinter import filedialog, messagebox
import customtkinter as ctk
from DrissionPage import ChromiumPage, ChromiumOptions


class CookieExtractor:
    """Cookie提取器核心类"""
    
    def __init__(self):
        self.page = None
        self.platforms = {
            "头条": {"url": "https://mp.toutiao.com/", "name": "toutiao"},
            "百家": {"url": "https://baijiahao.baidu.com/", "name": "baijiahao"},
            "抖音": {"url": "https://creator.douyin.com/", "name": "douyin"},
            "B站": {"url": "https://member.bilibili.com/", "name": "bilibili"},
            "闲鱼": {"url": "https://www.goofish.com/", "name": "xianyu"},
            "快手": {"url": "https://cp.kuaishou.com/", "name": "kuaishou"}
        }
    
    def start_browser(self, platform_url):
        """启动浏览器并打开指定平台"""
        try:
            # 配置浏览器选项
            options = ChromiumOptions()
            options.set_argument('--disable-blink-features=AutomationControlled')
            options.set_argument('--disable-web-security')
            options.set_argument('--allow-running-insecure-content')
            
            # 创建页面对象
            self.page = ChromiumPage(addr_or_opts=options)
            
            # 打开指定平台
            self.page.get(platform_url)
            return True
            
        except Exception as e:
            print(f"启动浏览器失败: {e}")
            return False
    
    def extract_cookies(self):
        """提取当前页面的Cookie"""
        if not self.page:
            return None
            
        try:
            # 获取所有Cookie
            cookies = self.page.get_cookies(as_dict=False)
            return cookies
            
        except Exception as e:
            print(f"提取Cookie失败: {e}")
            return None
    
    def format_cookies_to_txt(self, cookies, platform_name):
        """将Cookie格式化为txt格式"""
        if not cookies:
            return ""
            
        result = f"# Cookie for {platform_name}\n"
        result += f"# Generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        for cookie in cookies:
            # 标准Cookie txt格式：domain	flag	path	secure	expiration	name	value
            domain = cookie.get('domain', '')
            if not domain.startswith('.'):
                domain = '.' + domain
                
            flag = 'TRUE'
            path = cookie.get('path', '/')
            secure = 'TRUE' if cookie.get('secure', False) else 'FALSE'
            expires = cookie.get('expires', 0)
            if expires == -1:
                expires = 0
            name = cookie.get('name', '')
            value = cookie.get('value', '')
            
            result += f"{domain}\t{flag}\t{path}\t{secure}\t{expires}\t{name}\t{value}\n"
        
        return result
    
    def save_cookies(self, cookies_txt, save_path, platform_name):
        """保存Cookie到文件"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{platform_name}_cookies_{timestamp}.txt"
            filepath = os.path.join(save_path, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(cookies_txt)
            
            return filepath
            
        except Exception as e:
            print(f"保存Cookie失败: {e}")
            return None
    
    def logout_current_account(self):
        """退出当前账号（通用方法）"""
        try:
            # 尝试查找常见的退出按钮
            logout_selectors = [
                'a[href*="logout"]',
                'button[onclick*="logout"]',
                '.logout',
                '.sign-out',
                '[data-action="logout"]'
            ]
            
            for selector in logout_selectors:
                try:
                    logout_btn = self.page.ele(selector, timeout=2)
                    if logout_btn:
                        logout_btn.click()
                        time.sleep(2)
                        return True
                except:
                    continue
                    
            return False
            
        except Exception as e:
            print(f"退出账号失败: {e}")
            return False
    
    def close_browser(self):
        """关闭浏览器"""
        if self.page:
            try:
                self.page.quit()
                self.page = None
            except:
                pass


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        
        # 默认配置
        return {
            "save_path": os.path.expanduser("~/Desktop"),
            "selected_platform": "头条"
        }
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def get(self, key, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """设置配置项"""
        self.config[key] = value
        self.save_config()


class CookieExtractorGUI:
    """GUI界面类"""
    
    def __init__(self):
        # 设置外观
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        # 初始化组件
        self.extractor = CookieExtractor()
        self.config_manager = ConfigManager()
        
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("多平台Cookie提取工具")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 界面变量
        self.selected_platform = ctk.StringVar(value=self.config_manager.get("selected_platform", "头条"))
        self.save_path = ctk.StringVar(value=self.config_manager.get("save_path", os.path.expanduser("~/Desktop")))
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = ctk.CTkLabel(
            self.root, 
            text="多平台Cookie提取工具", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 平台选择框
        platform_frame = ctk.CTkFrame(self.root)
        platform_frame.pack(pady=10, padx=20, fill="x")
        
        platform_label = ctk.CTkLabel(platform_frame, text="选择平台:", font=ctk.CTkFont(size=14))
        platform_label.pack(pady=(10, 5))
        
        # 平台单选按钮
        platforms_container = ctk.CTkFrame(platform_frame)
        platforms_container.pack(pady=(0, 10), padx=10, fill="x")
        
        platforms = ["头条", "百家", "抖音", "B站", "闲鱼", "快手"]
        for i, platform in enumerate(platforms):
            row = i // 3
            col = i % 3
            
            radio_btn = ctk.CTkRadioButton(
                platforms_container,
                text=platform,
                variable=self.selected_platform,
                value=platform,
                command=self.on_platform_changed
            )
            radio_btn.grid(row=row, column=col, padx=10, pady=5, sticky="w")
        
        # 保存路径选择
        path_frame = ctk.CTkFrame(self.root)
        path_frame.pack(pady=10, padx=20, fill="x")
        
        path_label = ctk.CTkLabel(path_frame, text="保存路径:", font=ctk.CTkFont(size=14))
        path_label.pack(pady=(10, 5))
        
        path_container = ctk.CTkFrame(path_frame)
        path_container.pack(pady=(0, 10), padx=10, fill="x")
        
        self.path_entry = ctk.CTkEntry(path_container, textvariable=self.save_path, width=300)
        self.path_entry.pack(side="left", padx=(0, 10), fill="x", expand=True)
        
        browse_btn = ctk.CTkButton(path_container, text="浏览", command=self.browse_save_path, width=80)
        browse_btn.pack(side="right")
        
        # 操作按钮
        button_frame = ctk.CTkFrame(self.root)
        button_frame.pack(pady=20, padx=20, fill="x")
        
        self.start_browser_btn = ctk.CTkButton(
            button_frame,
            text="启动浏览器",
            command=self.start_browser_thread,
            width=150,
            height=40
        )
        self.start_browser_btn.pack(side="left", padx=(10, 5))
        
        self.extract_cookies_btn = ctk.CTkButton(
            button_frame,
            text="获取当前CK",
            command=self.extract_cookies_thread,
            width=150,
            height=40,
            state="disabled"
        )
        self.extract_cookies_btn.pack(side="right", padx=(5, 10))
        
        # 状态显示区域
        status_frame = ctk.CTkFrame(self.root)
        status_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        status_label = ctk.CTkLabel(status_frame, text="状态信息:", font=ctk.CTkFont(size=14))
        status_label.pack(pady=(10, 5))
        
        self.status_text = ctk.CTkTextbox(status_frame, height=100)
        self.status_text.pack(pady=(0, 10), padx=10, fill="both", expand=True)
        
        # 初始状态
        self.log_status("程序已启动，请选择平台并点击'启动浏览器'")
    
    def log_status(self, message):
        """记录状态信息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        self.status_text.insert("end", log_message)
        self.status_text.see("end")
        self.root.update()
    
    def on_platform_changed(self):
        """平台选择改变时的回调"""
        self.config_manager.set("selected_platform", self.selected_platform.get())
        self.log_status(f"已选择平台: {self.selected_platform.get()}")
    
    def browse_save_path(self):
        """浏览保存路径"""
        path = filedialog.askdirectory(initialdir=self.save_path.get())
        if path:
            self.save_path.set(path)
            self.config_manager.set("save_path", path)
            self.log_status(f"保存路径已设置: {path}")
    
    def start_browser_thread(self):
        """在新线程中启动浏览器"""
        threading.Thread(target=self.start_browser, daemon=True).start()
    
    def start_browser(self):
        """启动浏览器"""
        try:
            self.start_browser_btn.configure(state="disabled")
            self.log_status("正在启动浏览器...")
            
            platform = self.selected_platform.get()
            platform_url = self.extractor.platforms[platform]["url"]
            
            success = self.extractor.start_browser(platform_url)
            
            if success:
                self.log_status(f"浏览器已启动，已打开{platform}平台")
                self.log_status("请在浏览器中完成登录，然后点击'获取当前CK'")
                self.extract_cookies_btn.configure(state="normal")
            else:
                self.log_status("启动浏览器失败")
                self.start_browser_btn.configure(state="normal")
                
        except Exception as e:
            self.log_status(f"启动浏览器出错: {e}")
            self.start_browser_btn.configure(state="normal")
    
    def extract_cookies_thread(self):
        """在新线程中提取Cookie"""
        threading.Thread(target=self.extract_cookies, daemon=True).start()
    
    def extract_cookies(self):
        """提取Cookie"""
        try:
            self.extract_cookies_btn.configure(state="disabled")
            self.log_status("正在提取Cookie...")
            
            # 提取Cookie
            cookies = self.extractor.extract_cookies()
            
            if not cookies:
                self.log_status("未能获取到Cookie，请确保已登录")
                self.extract_cookies_btn.configure(state="normal")
                return
            
            # 格式化Cookie
            platform = self.selected_platform.get()
            platform_name = self.extractor.platforms[platform]["name"]
            cookies_txt = self.extractor.format_cookies_to_txt(cookies, platform_name)
            
            # 保存Cookie
            save_path = self.save_path.get()
            filepath = self.extractor.save_cookies(cookies_txt, save_path, platform_name)
            
            if filepath:
                self.log_status(f"Cookie已保存到: {filepath}")
                self.log_status(f"共提取到 {len(cookies)} 个Cookie")
                
                # 尝试退出账号
                self.log_status("正在尝试退出当前账号...")
                logout_success = self.extractor.logout_current_account()
                
                if logout_success:
                    self.log_status("已成功退出当前账号")
                else:
                    self.log_status("未能自动退出账号，请手动退出")
                
                messagebox.showinfo("成功", f"Cookie提取完成！\n保存位置: {filepath}")
            else:
                self.log_status("保存Cookie失败")
            
            self.extract_cookies_btn.configure(state="normal")
            
        except Exception as e:
            self.log_status(f"提取Cookie出错: {e}")
            self.extract_cookies_btn.configure(state="normal")
    
    def run(self):
        """运行GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        """关闭程序时的清理工作"""
        try:
            self.extractor.close_browser()
        except:
            pass
        self.root.destroy()


if __name__ == "__main__":
    app = CookieExtractorGUI()
    app.run()
